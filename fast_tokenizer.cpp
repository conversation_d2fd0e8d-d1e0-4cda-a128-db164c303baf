#include "fast_tokenizer.hpp"
#include <algorithm>
#include <sstream>

std::unordered_map<std::string, int> FastTokenizer::process_text_fast(
    const std::string& text, 
    const std::string& pattern,
    const std::vector<std::string>& special_tokens
) {
    std::unordered_map<std::string, int> counter;
    
    try {
        // Create special tokens pattern
        std::string special_pattern = create_special_tokens_pattern(special_tokens);
        
        std::regex token_regex(pattern);
        
        if (!special_tokens.empty() && !special_pattern.empty()) {
            std::regex special_regex(special_pattern);
            
            // Split by special tokens
            std::sregex_token_iterator iter(text.begin(), text.end(), special_regex, -1);
            std::sregex_token_iterator end;
            
            for (; iter != end; ++iter) {
                std::string segment = iter->str();
                if (!segment.empty()) {
                    tokenize_segment(segment, token_regex, counter);
                }
            }
        } else {
            // No special tokens, process entire text
            tokenize_segment(text, token_regex, counter);
        }
        
    } catch (const std::regex_error& e) {
        std::cerr << "Regex error in process_text_fast: " << e.what() << std::endl;
        // Fallback: simple whitespace tokenization
        std::istringstream iss(text);
        std::string token;
        while (iss >> token) {
            counter[token]++;
        }
    }
    
    return counter;
}

std::unordered_map<std::string, int> FastTokenizer::merge_counters(
    const std::unordered_map<std::string, int>& counter1,
    const std::unordered_map<std::string, int>& counter2
) {
    std::unordered_map<std::string, int> result = counter1;
    
    for (const auto& pair : counter2) {
        result[pair.first] += pair.second;
    }
    
    return result;
}

void FastTokenizer::update_counter_with_text(
    std::unordered_map<std::string, int>& counter,
    const std::string& text,
    const std::string& pattern,
    const std::vector<std::string>& special_tokens
) {
    auto new_tokens = process_text_fast(text, pattern, special_tokens);
    
    for (const auto& pair : new_tokens) {
        counter[pair.first] += pair.second;
    }
}

std::unordered_map<std::string, int> FastTokenizer::convert_from_python_counter(
    const std::vector<std::pair<std::vector<unsigned char>, int>>& py_counter_items
) {
    std::unordered_map<std::string, int> cpp_counter;
    
    for (const auto& item : py_counter_items) {
        // Convert vector of bytes to string
        std::string token_str(item.first.begin(), item.first.end());
        cpp_counter[token_str] = item.second;
    }
    
    return cpp_counter;
}

std::vector<std::pair<std::vector<unsigned char>, int>> FastTokenizer::convert_to_python_counter(
    const std::unordered_map<std::string, int>& cpp_counter
) {
    std::vector<std::pair<std::vector<unsigned char>, int>> py_counter_items;
    
    for (const auto& pair : cpp_counter) {
        // Convert string to vector of bytes
        std::vector<unsigned char> token_bytes(pair.first.begin(), pair.first.end());
        py_counter_items.emplace_back(token_bytes, pair.second);
    }
    
    return py_counter_items;
}

void FastTokenizer::tokenize_segment(
    const std::string& segment,
    const std::regex& token_regex,
    std::unordered_map<std::string, int>& counter
) {
    std::sregex_iterator token_iter(segment.begin(), segment.end(), token_regex);
    std::sregex_iterator token_end;
    
    for (; token_iter != token_end; ++token_iter) {
        std::string token = token_iter->str();
        if (!token.empty()) {
            counter[token]++;
        }
    }
}

std::string FastTokenizer::create_special_tokens_pattern(
    const std::vector<std::string>& special_tokens
) {
    if (special_tokens.empty()) {
        return "";
    }
    
    std::string pattern = "";
    for (size_t i = 0; i < special_tokens.size(); ++i) {
        if (i > 0) {
            pattern += "|";
        }
        // Escape special regex characters in the token
        std::string escaped_token = special_tokens[i];
        // Simple escaping for common regex special characters
        std::string special_chars = "\\^$.|?*+()[]{}";
        for (char c : special_chars) {
            size_t pos = 0;
            std::string char_str(1, c);
            std::string escaped_char = "\\" + char_str;
            while ((pos = escaped_token.find(char_str, pos)) != std::string::npos) {
                escaped_token.replace(pos, 1, escaped_char);
                pos += escaped_char.length();
            }
        }
        pattern += escaped_token;
    }
    
    return pattern;
}

// TokenizerUtils implementation
void TokenizerUtils::print_counter(
    const std::unordered_map<std::string, int>& counter,
    int max_items
) {
    std::cout << "Counter contents (showing up to " << max_items << " items):" << std::endl;
    
    int count = 0;
    for (const auto& pair : counter) {
        if (count >= max_items) break;
        std::cout << "  '" << pair.first << "': " << pair.second << std::endl;
        count++;
    }
    
    if (counter.size() > max_items) {
        std::cout << "  ... and " << (counter.size() - max_items) << " more items" << std::endl;
    }
}

int TokenizerUtils::get_total_count(
    const std::unordered_map<std::string, int>& counter
) {
    int total = 0;
    for (const auto& pair : counter) {
        total += pair.second;
    }
    return total;
}

std::vector<std::pair<std::string, int>> TokenizerUtils::get_most_frequent(
    const std::unordered_map<std::string, int>& counter,
    int top_k
) {
    std::vector<std::pair<std::string, int>> items(counter.begin(), counter.end());
    
    // Sort by frequency (descending)
    std::sort(items.begin(), items.end(), 
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // Return top k items
    if (items.size() > top_k) {
        items.resize(top_k);
    }
    
    return items;
}
