# cppyy Dummy Tokenizer Integration

这个项目演示了如何使用cppyy将C++代码集成到Python中，特别是如何让C++方法接受和处理Python的`collections.Counter`对象。

## 文件结构

- `tokenizer_dummy.hpp` - C++头文件，定义了dummy tokenizer类
- `tokenizer_dummy.cpp` - C++实现文件，包含dummy方法的具体实现
- `tokenizer_dummy.py` - Python模块，使用cppyy加载C++代码并提供Python接口
- `assign1.py` - 主Python文件，调用dummy tokenizer方法
- `setup_cppyy.py` - 安装和测试脚本

## 安装和配置

### 1. 安装cppyy

```bash
pip install cppyy
```

或者运行自动安装脚本：

```bash
python setup_cppyy.py
```

### 2. 验证安装

运行测试脚本来验证cppyy是否正确安装：

```bash
python tokenizer_dummy.py
```

## 使用方法

### 基本用法

```python
from collections import Counter
from tokenizer_dummy import process_counter_dummy, create_dummy_counter

# 创建一个Counter对象
counter = Counter({
    tuple(b'hello'): 5,
    tuple(b'world'): 3,
    tuple(b'test'): 2
})

# 使用C++方法处理Counter
total_count = process_counter_dummy(counter)
print(f"Total tokens: {total_count}")

# 使用C++方法创建dummy Counter
text = "This is a test text"
dummy_counter = create_dummy_counter(text)
print(f"Created counter: {dict(dummy_counter)}")
```

### 在assign1.py中的集成

运行主程序来看演示：

```bash
python assign1.py
```

这将展示：
1. 如何将Python Counter传递给C++方法
2. 如何从C++方法返回数据到Python Counter
3. C++和Python fallback的对比

## 技术细节

### C++方法签名

```cpp
// 处理Counter对象
static int process_counter(const std::unordered_map<std::string, int>& counter);

// 创建dummy Counter
static std::unordered_map<std::string, int> create_dummy_counter(const std::string& text);
```

### Python-C++数据转换

1. **Python Counter → C++ unordered_map**:
   - Python的tuple keys被转换为string
   - 保持频次值不变

2. **C++ unordered_map → Python Counter**:
   - C++的string keys被转换为tuple of bytes
   - 保持频次值不变

### 错误处理

如果cppyy不可用或C++代码加载失败，系统会自动回退到Python实现，确保程序仍能正常运行。

## 扩展这个例子

这个dummy实现可以作为模板来创建更复杂的tokenization功能：

1. 将dummy方法替换为真实的tokenization逻辑
2. 添加更多的C++优化算法
3. 实现更复杂的数据结构转换

## 故障排除

### 常见问题

1. **cppyy安装失败**
   - 确保有C++编译器（如gcc或clang）
   - 在某些系统上可能需要安装开发工具

2. **C++文件未找到**
   - 确保`.hpp`和`.cpp`文件在正确的目录中
   - 检查文件路径和权限

3. **编译错误**
   - 检查C++代码语法
   - 确保包含了必要的头文件

### 调试

启用详细输出来调试问题：

```python
import cppyy
cppyy.set_debug(True)
```

## 性能说明

这是一个dummy实现，主要用于演示集成方法。在实际应用中，C++实现应该会比Python实现快得多，特别是对于大量数据的处理。
