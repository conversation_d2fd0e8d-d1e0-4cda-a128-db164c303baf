============================= test session starts ==============================
platform linux -- Python 3.12.6, pytest-8.3.5, pluggy-1.5.0 -- /home/<USER>/cs336/assignment1-basics/.venv/bin/python3
cachedir: .pytest_cache
rootdir: /home/<USER>/cs336/assignment1-basics
configfile: pyproject.toml
plugins: jaxtyping-0.3.1
collecting ... collected 1 item

tests/test_tokenizer.py::test_address_matches_tiktoken 50257
FAILED

=================================== FAILURES ===================================
________________________ test_address_matches_tiktoken _________________________

    def test_address_matches_tiktoken():
        reference_tokenizer = tiktoken.get_encoding("gpt2")
        tokenizer = get_tokenizer_from_vocab_merges_path(
            vocab_path=VOCAB_PATH,
            merges_path=MERGES_PATH,
        )
        corpus_path = FIXTURES_PATH / "address.txt"
        with open(corpus_path) as f:
            corpus_contents = f.read()
        reference_ids = reference_tokenizer.encode(corpus_contents)
        ids = tokenizer.encode(corpus_contents)
>       assert ids == reference_ids
E       assert [37, 454, 629, 382, 290, 384, 574, 812, 2084, 267, 333, 3735, 7084, 1379, 8951, 6071, 11, 319, 294, 271, 542, 7233, 11, 257, 649, 34664, 295, 11, 8571, 1572, 287, 7980, 861, 88, 11, 290, 4648, 3970, 1513, 284, 262, 2632, 7434, 295, 326, 477, 1450, 389, 1126, 515, 1602, 282, 13, 198, 3844, 356, 389, 1786, 1886, 287, 257, 10536, 265, 269, 452, 346, 1175, 11, 573, 301, 278, 483, 490, 326, 34664, 295, 11, 393, 597, 34664, 295, 523, 8571, 1572, 290, 523, 4648, 3970, 1513, 11, 460, 2376, 782, 886, 495, 13, 775, 389, 1138, 319, 257, 10536, 265, 6274, 293, 12, 12463, 417, 67, 286, 326, 1175, 13, 775, 387, 303, 1282, 284, 4648, 3970, 660, 257, 2493, 295, 286, 326, 25912, 417, 67, 11, 355, 257, 957, 282, 1334, 278, 458, 558, 329, 42796, 325, 508, 994, 31986, 303, 262, 343, 3160, 326, 326, 34664, 295, 37011, 4352, 2107, 13, 632, 318, 5988, 519, 6750, 43500, 278, 290, 1774, 326, 356, 7354, 32926, 466, 294, 271, 13, 198, 1537, 11, 287, 257, 4025, 2565, 11, 356, 460, 407, 4648, 3970, 660, 960, 732, 460, 407, 762, 721, 4873, 960, 732, 460, 407, 6899, 322, 960, 400, 271, 1132, 358, 13, 383, 14802, 1450, 11, 17717, 278, 290, 390, 324, 11, 508, 2874, 1130, 992, 994, 11, 387, 303, 762, 721, 4111, 340, 11, 1290, 450, 659, 267, 333, 745, 273, 7182, 263, 284, 751, 393, 1062, 974, 13, 383, 476, 335, 481, 6578, 7100, 3465, 11, 4249, 2376, 782, 816, 24419, 263, 348, 265, 356, 910, 994, 11, 475, 340, 460, 497, 332, 6044, 348, 265, 484, 750, 994, 13, 632, 318, 329, 514, 262, 17717, 278, 11, 4227, 372, 11, 284, 307, 4648, 3970, 1513, 994, 284, 262, 3684, 16661, 704, 670, 348, 488, 484, 508, 46287, 456, 83, 994, 387, 303, 294, 385, 1290, 523, 31801, 306, 1354, 2903, 13, 632, 318, 4227, 372, 329, 514, 284, 307, 994, 4648, 3970, 1513, 284, 262, 10536, 265, 20486, 8135, 816, 391, 278, 307, 754, 514, 960, 5562, 422, 262, 325, 3032, 1850, 390, 324, 356, 20486, 365, 753, 21468, 36622, 1614, 5092, 261, 284, 326, 1275, 1904, 329, 348, 488, 484, 31986, 303, 262, 938, 1336, 502, 27345, 260, 286, 1614, 5092, 261, 960, 5562, 356, 994, 1029, 306, 581, 349, 303, 326, 262, 325, 390, 324, 427, 439, 407, 387, 303, 3724, 287, 46935, 259, 960, 5562, 294, 271, 34664, 295, 11, 739, 1793, 11, 427, 439, 387, 303, 257, 649, 35122, 400, 286, 13459, 296, 960, 392, 326, 467, 933, 434, 286, 262, 613, 643, 11, 416, 262, 613, 643, 11, 329, 262, 613, 643, 11, 427, 439, 407, 583, 680, 422, 262, 304, 11999, 13, 198] == [15137, 4776, 290, 3598, 812, 2084, 674, 17150, 3181, 6071, 11, 319, 428, 15549, 11, 257, 649, 3277, 11, 21581, 287, 14734, 11, 290, 7256, 284, 262, 19168, 326, 477, 1450, 389, 2727, 4961, 13, 198, 3844, 356, 389, 7953, 287, 257, 1049, 3026, 1175, 11, 4856, 1771, 326, 3277, 11, 393, 597, 3277, 523, 21581, 290, 523, 7256, 11, 460, 890, 21178, 13, 775, 389, 1138, 319, 257, 1049, 3344, 12, 3245, 286, 326, 1175, 13, 775, 423, 1282, 284, 39383, 257, 6903, 286, 326, 2214, 11, 355, 257, 2457, 19186, 1295, 329, 883, 508, 994, 2921, 511, 3160, 326, 326, 3277, 1244, 2107, 13, 632, 318, 13318, 15830, 290, 1774, 326, 356, 815, 466, 428, 13, 198, 1537, 11, 287, 257, 4025, 2565, 11, 356, 460, 407, 39383, 960, 732, 460, 407, 42938, 4873, 960, 732, 460, 407, 6899, 322, 960, 5661, 2323, 13, 383, 14802, 1450, 11, 2877, 290, 2636, 11, 508, 11615, 994, 11, 423, 42938, 4111, 340, 11, 1290, 2029, 674, 3595, 1176, 284, 751, 393, 31041, 13, 383, 995, 481, 1310, 3465, 11, 4249, 890, 3505, 644, 356, 910, 994, 11, 475, 340, 460, 1239, 6044, 644, 484, 750, 994, 13, 632, 318, 329, 514, 262, 2877, 11, 2138, 11, 284, 307, 7256, 994, 284, 262, 34419, 670, 543, 484, 508, 8350, 994, 423, 4145, 1290, 523, 31801, 306, 6190, 13, 632, 318, 2138, 329, 514, 284, 307, 994, 7256, 284, 262, 1049, 4876, 5637, 878, 514, 960, 5562, 422, 777, 21014, 2636, 356, 1011, 3220, 25808, 284, 326, 2728, 329, 543, 484, 2921, 262, 938, 1336, 3953, 286, 25808, 960, 5562, 356, 994, 4047, 10568, 326, 777, 2636, 2236, 407, 423, 3724, 287, 23469, 960, 5562, 428, 3277, 11, 739, 1793, 11, 2236, 423, 257, 649, 4082, 286, 4925, 960, 392, 326, 1230, 286, 262, 661, 11, 416, 262, 661, 11, 329, 262, 661, 11, 2236, 407, 42531, 422, 262, 4534, 13, 198]
E         
E         At index 0 diff: 37 != 15137
E         Left contains 136 more items, first extra item: 4227
E         
E         Full diff:
E           [
E         -     15137,
E         ?     ---
E         +     37,
E         -     4776,
E         +     454,
E         +     629,
E         +     382,
E               290,
E         -     3598,
E         ?      --
E         +     384,
E         ?       +
E         +     574,
E               812,
E               2084,
E         -     674,
E         ?       -
E         +     267,
E         ?     +
E         -     17150,
E         +     333,
E         +     3735,
E         +     7084,
E         +     1379,
E         -     3181,
E         ?     --
E         +     8951,
E         ?      ++
E               6071,
E               11,
E               319,
E         +     294,
E         +     271,
E         -     428,
E         ?       -
E         +     542,
E         ?     +
E         -     15549,
E         +     7233,
E               11,
E               257,
E               649,
E         -     3277,
E         -     11,
E         +     34664,
E         +     295,
E         -     21581,
E         ?     - --
E         +     11,
E         +     8571,
E         +     1572,
E               287,
E         -     14734,
E         +     7980,
E         +     861,
E         +     88,
E               11,
E               290,
E         +     4648,
E         +     3970,
E         +     1513,
E         +     284,
E         -     7256,
E         ?     - -
E         +     262,
E         ?       +
E         -     284,
E         -     262,
E         +     2632,
E         ?       +
E         -     19168,
E         +     7434,
E         +     295,
E               326,
E               477,
E               1450,
E               389,
E         +     1126,
E         +     515,
E         +     1602,
E         -     2727,
E         ?      ^ -
E         +     282,
E         ?      ^
E         -     4961,
E               13,
E               198,
E               3844,
E               356,
E               389,
E         -     7953,
E         +     1786,
E         +     1886,
E               287,
E               257,
E         -     1049,
E         +     10536,
E         -     3026,
E         ?     --
E         +     265,
E         ?       +
E         +     269,
E         +     452,
E         +     346,
E               1175,
E               11,
E         -     4856,
E         -     1771,
E         -     326,
E         ?      --
E         +     573,
E         ?     ++
E         +     301,
E         -     3277,
E         ?     -  ^
E         +     278,
E         ?       ^
E         +     483,
E         +     490,
E         +     326,
E         +     34664,
E         +     295,
E               11,
E               393,
E               597,
E         -     3277,
E         +     34664,
E         +     295,
E               523,
E         -     21581,
E         +     8571,
E         +     1572,
E               290,
E               523,
E         -     7256,
E         +     4648,
E         +     3970,
E         +     1513,
E               11,
E               460,
E         -     890,
E         +     2376,
E         -     21178,
E         ?     ---
E         +     782,
E         ?       +
E         +     886,
E         +     495,
E               13,
E               775,
E               389,
E               1138,
E               319,
E               257,
E         -     1049,
E         -     3344,
E         +     10536,
E         +     265,
E         +     6274,
E         +     293,
E               12,
E         -     3245,
E         +     12463,
E         +     417,
E         +     67,
E               286,
E               326,
E               1175,
E               13,
E               775,
E         -     423,
E         ?     --
E         +     387,
E         ?      ++
E         +     303,
E               1282,
E               284,
E         -     39383,
E         +     4648,
E         +     3970,
E         +     660,
E         +     257,
E         +     2493,
E         -     257,
E         ?       -
E         +     295,
E         ?      +
E         -     6903,
E               286,
E               326,
E         -     2214,
E         +     25912,
E         +     417,
E         +     67,
E               11,
E               355,
E               257,
E         -     2457,
E         ?     ^^
E         +     957,
E         ?     ^
E         -     19186,
E         -     1295,
E         +     282,
E         +     1334,
E         +     278,
E         +     458,
E         +     558,
E               329,
E         +     42796,
E         -     883,
E         ?     --
E         +     325,
E         ?      ++
E               508,
E               994,
E         +     31986,
E         +     303,
E         -     2921,
E         ?      ^ -
E         +     262,
E         ?      ^
E         -     511,
E         +     343,
E               3160,
E               326,
E               326,
E         +     34664,
E         +     295,
E         +     37011,
E         -     3277,
E         ?       --
E         +     4352,
E         ?     + +
E         -     1244,
E               2107,
E               13,
E               632,
E               318,
E         -     13318,
E         -     15830,
E         +     5988,
E         +     519,
E         +     6750,
E         +     43500,
E         +     278,
E               290,
E               1774,
E               326,
E               356,
E         -     815,
E         +     7354,
E         +     32926,
E               466,
E         -     428,
E         ?      --
E         +     294,
E         ?     ++
E         +     271,
E               13,
E               198,
E               1537,
E               11,
E               287,
E               257,
E               4025,
E               2565,
E               11,
E               356,
E               460,
E               407,
E         -     39383,
E         +     4648,
E         +     3970,
E         +     660,
E               960,
E               732,
E               460,
E               407,
E         -     42938,
E         +     762,
E         +     721,
E               4873,
E               960,
E               732,
E               460,
E               407,
E               6899,
E               322,
E               960,
E         -     5661,
E         +     400,
E         +     271,
E         -     2323,
E         ?     ^  -
E         +     1132,
E         ?     ^^
E         +     358,
E               13,
E               383,
E               14802,
E               1450,
E               11,
E         +     17717,
E         -     2877,
E         ?       --
E         +     278,
E         ?      +
E               290,
E         -     2636,
E         +     390,
E         +     324,
E               11,
E               508,
E         +     2874,
E         -     11615,
E         -     994,
E         -     11,
E         +     1130,
E         ?       ++
E         -     423,
E         ?     ^ -
E         +     992,
E         ?     ^^
E         +     994,
E         +     11,
E         -     42938,
E         ?     ---
E         +     387,
E         ?       +
E         +     303,
E         +     762,
E         +     721,
E               4111,
E               340,
E               11,
E               1290,
E         -     2029,
E         +     450,
E         +     659,
E         -     674,
E         ?       -
E         +     267,
E         ?     +
E         -     3595,
E         -     1176,
E         +     333,
E         +     745,
E         +     273,
E         +     7182,
E         +     263,
E               284,
E               751,
E               393,
E         -     31041,
E         +     1062,
E         +     974,
E               13,
E               383,
E         +     476,
E         -     995,
E         ?     ^^
E         +     335,
E         ?     ^^
E               481,
E         +     6578,
E         -     1310,
E         ?     ^^
E         +     7100,
E         ?     ^  +
E               3465,
E               11,
E               4249,
E         +     2376,
E         -     890,
E         ?      ^^
E         +     782,
E         ?     + ^
E         -     3505,
E         +     816,
E         -     644,
E         ?     ^
E         +     24419,
E         ?     ^  ++
E         +     263,
E         +     348,
E         +     265,
E               356,
E               910,
E               994,
E               11,
E               475,
E               340,
E               460,
E         -     1239,
E         +     497,
E         +     332,
E               6044,
E         -     644,
E         ?     ^ ^
E         +     348,
E         ?     ^ ^
E         +     265,
E               484,
E               750,
E               994,
E               13,
E               632,
E               318,
E               329,
E               514,
E               262,
E         -     2877,
E         -     11,
E         +     17717,
E         ?      ++ +
E         -     2138,
E         ?      ^^
E         +     278,
E         ?      ^
E         +     11,
E         +     4227,
E         +     372,
E               11,
E               284,
E               307,
E         +     4648,
E         +     3970,
E         +     1513,
E         +     994,
E         +     284,
E         -     7256,
E         ?     - -
E         +     262,
E         ?       +
E         -     994,
E         -     284,
E         ?     ^
E         +     3684,
E         ?     ^^
E         -     262,
E         -     34419,
E         +     16661,
E         +     704,
E               670,
E         -     543,
E         ?     ^ ^
E         +     348,
E         ?     ^ ^
E         +     488,
E               484,
E               508,
E         +     46287,
E         +     456,
E         -     8350,
E         ?       --
E         +     83,
E         +     994,
E         +     387,
E         +     303,
E         -     994,
E         ?     ^
E         +     294,
E         ?     ^
E         -     423,
E         ?     --
E         +     385,
E         ?      ++
E         -     4145,
E               1290,
E               523,
E               31801,
E               306,
E         +     1354,
E         -     6190,
E         ?     ^^
E         +     2903,
E         ?     ^  +
E               13,
E               632,
E               318,
E         -     2138,
E         +     4227,
E         +     372,
E               329,
E               514,
E               284,
E               307,
E               994,
E         +     4648,
E         +     3970,
E         +     1513,
E         +     284,
E         -     7256,
E         ?     - -
E         +     262,
E         ?       +
E         -     284,
E         +     10536,
E         -     262,
E         ?       ^
E         +     265,
E         ?       ^
E         -     1049,
E         -     4876,
E         ?       -
E         +     20486,
E         ?     ++
E         -     5637,
E         +     8135,
E         +     816,
E         +     391,
E         -     878,
E         ?     ^
E         +     278,
E         ?     ^
E         +     307,
E         +     754,
E               514,
E               960,
E               5562,
E               422,
E         -     777,
E         -     21014,
E         -     2636,
E         -     356,
E         -     1011,
E         -     3220,
E         -     25808,
E         -     284,
E         -     326,
E         ?     -
E         +     262,
E         ?       +
E         +     325,
E         +     3032,
E         +     1850,
E         +     390,
E         +     324,
E         +     356,
E         +     20486,
E         +     365,
E         +     753,
E         +     21468,
E         +     36622,
E         +     1614,
E         +     5092,
E         +     261,
E         -     2728,
E         ?     --
E         +     284,
E         ?       +
E         +     326,
E         +     1275,
E         +     1904,
E               329,
E         -     543,
E         ?     ^ ^
E         +     348,
E         ?     ^ ^
E         +     488,
E               484,
E         -     2921,
E         +     31986,
E         +     303,
E               262,
E               938,
E               1336,
E         -     3953,
E         +     502,
E         +     27345,
E         +     260,
E               286,
E         -     25808,
E         +     1614,
E         +     5092,
E         +     261,
E               960,
E               5562,
E               356,
E               994,
E         +     1029,
E         -     4047,
E         -     10568,
E         -     326,
E         ?      ^
E         +     306,
E         ?      ^
E         -     777,
E         +     581,
E         +     349,
E         +     303,
E         -     2636,
E         ?       --
E         +     326,
E         ?     +
E         -     2236,
E         ?       --
E         +     262,
E         ?      +
E         +     325,
E         +     390,
E         +     324,
E         +     427,
E         +     439,
E               407,
E         -     423,
E         ?     --
E         +     387,
E         ?      ++
E         +     303,
E               3724,
E               287,
E         -     23469,
E         ?     --
E         +     46935,
E         ?        ++
E         +     259,
E               960,
E               5562,
E         -     428,
E         ?      --
E         +     294,
E         ?     ++
E         -     3277,
E         ?     -  ^
E         +     271,
E         ?       ^
E         +     34664,
E         +     295,
E               11,
E               739,
E               1793,
E               11,
E         -     2236,
E         -     423,
E         ?       ^
E         +     427,
E         ?       ^
E         +     439,
E         +     387,
E         +     303,
E               257,
E               649,
E         +     35122,
E         -     4082,
E         ?       ^^
E         +     400,
E         ?       ^
E               286,
E         -     4925,
E         +     13459,
E         +     296,
E               960,
E               392,
E               326,
E         -     1230,
E         +     467,
E         +     933,
E         +     434,
E               286,
E               262,
E         -     661,
E         ?     -
E         +     613,
E         ?       +
E         +     643,
E               11,
E               416,
E               262,
E         -     661,
E         ?     -
E         +     613,
E         ?       +
E         +     643,
E               11,
E               329,
E               262,
E         -     661,
E         ?     -
E         +     613,
E         ?       +
E         +     643,
E               11,
E         -     2236,
E         +     427,
E         +     439,
E               407,
E         -     42531,
E         ?     --  -
E         +     583,
E         ?      +
E         +     680,
E               422,
E               262,
E         -     4534,
E         ?     --
E         +     304,
E         ?      +
E         +     11999,
E               13,
E               198,
E           ]

tests/test_tokenizer.py:290: AssertionError
=========================== short test summary info ============================
FAILED tests/test_tokenizer.py::test_address_matches_tiktoken - assert [37, 454, 629, 382, 290, 384, 574, 812, 2084, 267, 333, 3735, 7084, 1379, 8951, 6071, 11, 319, 294, 271, 542, 7233, 11, 257, 649, 34664, 295, 11, 8571, 1572, 287, 7980, 861, 88, 11, 290, 4648, 3970, 1513, 284, 262, 2632, 7434, 295, 326, 477, 1450, 389, 1126, 515, 1602, 282, 13, 198, 3844, 356, 389, 1786, 1886, 287, 257, 10536, 265, 269, 452, 346, 1175, 11, 573, 301, 278, 483, 490, 326, 34664, 295, 11, 393, 597, 34664, 295, 523, 8571, 1572, 290, 523, 4648, 3970, 1513, 11, 460, 2376, 782, 886, 495, 13, 775, 389, 1138, 319, 257, 10536, 265, 6274, 293, 12, 12463, 417, 67, 286, 326, 1175, 13, 775, 387, 303, 1282, 284, 4648, 3970, 660, 257, 2493, 295, 286, 326, 25912, 417, 67, 11, 355, 257, 957, 282, 1334, 278, 458, 558, 329, 42796, 325, 508, 994, 31986, 303, 262, 343, 3160, 326, 326, 34664, 295, 37011, 4352, 2107, 13, 632, 318, 5988, 519, 6750, 43500, 278, 290, 1774, 326, 356, 7354, 32926, 466, 294, 271, 13, 198, 1537, 11, 287, 257, 4025, 2565, 11, 356, 460, 407, 4648, 3970, 660, 960, 732, 460, 407, 762, 721, 4873, 960, 732, 460, 407, 6899, 322, 960, 400, 271, 1132, 358, 13, 383, 14802, 1450, 11, 17717, 278, 290, 390, 324, 11, 508, 2874, 1130, 992, 994, 11, 387, 303, 762, 721, 4111, 340, 11, 1290, 450, 659, 267, 333, 745, 273, 7182, 263, 284, 751, 393, 1062, 974, 13, 383, 476, 335, 481, 6578, 7100, 3465, 11, 4249, 2376, 782, 816, 24419, 263, 348, 265, 356, 910, 994, 11, 475, 340, 460, 497, 332, 6044, 348, 265, 484, 750, 994, 13, 632, 318, 329, 514, 262, 17717, 278, 11, 4227, 372, 11, 284, 307, 4648, 3970, 1513, 994, 284, 262, 3684, 16661, 704, 670, 348, 488, 484, 508, 46287, 456, 83, 994, 387, 303, 294, 385, 1290, 523, 31801, 306, 1354, 2903, 13, 632, 318, 4227, 372, 329, 514, 284, 307, 994, 4648, 3970, 1513, 284, 262, 10536, 265, 20486, 8135, 816, 391, 278, 307, 754, 514, 960, 5562, 422, 262, 325, 3032, 1850, 390, 324, 356, 20486, 365, 753, 21468, 36622, 1614, 5092, 261, 284, 326, 1275, 1904, 329, 348, 488, 484, 31986, 303, 262, 938, 1336, 502, 27345, 260, 286, 1614, 5092, 261, 960, 5562, 356, 994, 1029, 306, 581, 349, 303, 326, 262, 325, 390, 324, 427, 439, 407, 387, 303, 3724, 287, 46935, 259, 960, 5562, 294, 271, 34664, 295, 11, 739, 1793, 11, 427, 439, 387, 303, 257, 649, 35122, 400, 286, 13459, 296, 960, 392, 326, 467, 933, 434, 286, 262, 613, 643, 11, 416, 262, 613, 643, 11, 329, 262, 613, 643, 11, 427, 439, 407, 583, 680, 422, 262, 304, 11999, 13, 198] == [15137, 4776, 290, 3598, 812, 2084, 674, 17150, 3181, 6071, 11, 319, 428, 15549, 11, 257, 649, 3277, 11, 21581, 287, 14734, 11, 290, 7256, 284, 262, 19168, 326, 477, 1450, 389, 2727, 4961, 13, 198, 3844, 356, 389, 7953, 287, 257, 1049, 3026, 1175, 11, 4856, 1771, 326, 3277, 11, 393, 597, 3277, 523, 21581, 290, 523, 7256, 11, 460, 890, 21178, 13, 775, 389, 1138, 319, 257, 1049, 3344, 12, 3245, 286, 326, 1175, 13, 775, 423, 1282, 284, 39383, 257, 6903, 286, 326, 2214, 11, 355, 257, 2457, 19186, 1295, 329, 883, 508, 994, 2921, 511, 3160, 326, 326, 3277, 1244, 2107, 13, 632, 318, 13318, 15830, 290, 1774, 326, 356, 815, 466, 428, 13, 198, 1537, 11, 287, 257, 4025, 2565, 11, 356, 460, 407, 39383, 960, 732, 460, 407, 42938, 4873, 960, 732, 460, 407, 6899, 322, 960, 5661, 2323, 13, 383, 14802, 1450, 11, 2877, 290, 2636, 11, 508, 11615, 994, 11, 423, 42938, 4111, 340, 11, 1290, 2029, 674, 3595, 1176, 284, 751, 393, 31041, 13, 383, 995, 481, 1310, 3465, 11, 4249, 890, 3505, 644, 356, 910, 994, 11, 475, 340, 460, 1239, 6044, 644, 484, 750, 994, 13, 632, 318, 329, 514, 262, 2877, 11, 2138, 11, 284, 307, 7256, 994, 284, 262, 34419, 670, 543, 484, 508, 8350, 994, 423, 4145, 1290, 523, 31801, 306, 6190, 13, 632, 318, 2138, 329, 514, 284, 307, 994, 7256, 284, 262, 1049, 4876, 5637, 878, 514, 960, 5562, 422, 777, 21014, 2636, 356, 1011, 3220, 25808, 284, 326, 2728, 329, 543, 484, 2921, 262, 938, 1336, 3953, 286, 25808, 960, 5562, 356, 994, 4047, 10568, 326, 777, 2636, 2236, 407, 423, 3724, 287, 23469, 960, 5562, 428, 3277, 11, 739, 1793, 11, 2236, 423, 257, 649, 4082, 286, 4925, 960, 392, 326, 1230, 286, 262, 661, 11, 416, 262, 661, 11, 329, 262, 661, 11, 2236, 407, 42531, 422, 262, 4534, 13, 198]
  
  At index 0 diff: 37 != 15137
  Left contains 136 more items, first extra item: 4227
  
  Full diff:
    [
  -     15137,
  ?     ---
  +     37,
  -     4776,
  +     454,
  +     629,
  +     382,
        290,
  -     3598,
  ?      --
  +     384,
  ?       +
  +     574,
        812,
        2084,
  -     674,
  ?       -
  +     267,
  ?     +
  -     17150,
  +     333,
  +     3735,
  +     7084,
  +     1379,
  -     3181,
  ?     --
  +     8951,
  ?      ++
        6071,
        11,
        319,
  +     294,
  +     271,
  -     428,
  ?       -
  +     542,
  ?     +
  -     15549,
  +     7233,
        11,
        257,
        649,
  -     3277,
  -     11,
  +     34664,
  +     295,
  -     21581,
  ?     - --
  +     11,
  +     8571,
  +     1572,
        287,
  -     14734,
  +     7980,
  +     861,
  +     88,
        11,
        290,
  +     4648,
  +     3970,
  +     1513,
  +     284,
  -     7256,
  ?     - -
  +     262,
  ?       +
  -     284,
  -     262,
  +     2632,
  ?       +
  -     19168,
  +     7434,
  +     295,
        326,
        477,
        1450,
        389,
  +     1126,
  +     515,
  +     1602,
  -     2727,
  ?      ^ -
  +     282,
  ?      ^
  -     4961,
        13,
        198,
        3844,
        356,
        389,
  -     7953,
  +     1786,
  +     1886,
        287,
        257,
  -     1049,
  +     10536,
  -     3026,
  ?     --
  +     265,
  ?       +
  +     269,
  +     452,
  +     346,
        1175,
        11,
  -     4856,
  -     1771,
  -     326,
  ?      --
  +     573,
  ?     ++
  +     301,
  -     3277,
  ?     -  ^
  +     278,
  ?       ^
  +     483,
  +     490,
  +     326,
  +     34664,
  +     295,
        11,
        393,
        597,
  -     3277,
  +     34664,
  +     295,
        523,
  -     21581,
  +     8571,
  +     1572,
        290,
        523,
  -     7256,
  +     4648,
  +     3970,
  +     1513,
        11,
        460,
  -     890,
  +     2376,
  -     21178,
  ?     ---
  +     782,
  ?       +
  +     886,
  +     495,
        13,
        775,
        389,
        1138,
        319,
        257,
  -     1049,
  -     3344,
  +     10536,
  +     265,
  +     6274,
  +     293,
        12,
  -     3245,
  +     12463,
  +     417,
  +     67,
        286,
        326,
        1175,
        13,
        775,
  -     423,
  ?     --
  +     387,
  ?      ++
  +     303,
        1282,
        284,
  -     39383,
  +     4648,
  +     3970,
  +     660,
  +     257,
  +     2493,
  -     257,
  ?       -
  +     295,
  ?      +
  -     6903,
        286,
        326,
  -     2214,
  +     25912,
  +     417,
  +     67,
        11,
        355,
        257,
  -     2457,
  ?     ^^
  +     957,
  ?     ^
  -     19186,
  -     1295,
  +     282,
  +     1334,
  +     278,
  +     458,
  +     558,
        329,
  +     42796,
  -     883,
  ?     --
  +     325,
  ?      ++
        508,
        994,
  +     31986,
  +     303,
  -     2921,
  ?      ^ -
  +     262,
  ?      ^
  -     511,
  +     343,
        3160,
        326,
        326,
  +     34664,
  +     295,
  +     37011,
  -     3277,
  ?       --
  +     4352,
  ?     + +
  -     1244,
        2107,
        13,
        632,
        318,
  -     13318,
  -     15830,
  +     5988,
  +     519,
  +     6750,
  +     43500,
  +     278,
        290,
        1774,
        326,
        356,
  -     815,
  +     7354,
  +     32926,
        466,
  -     428,
  ?      --
  +     294,
  ?     ++
  +     271,
        13,
        198,
        1537,
        11,
        287,
        257,
        4025,
        2565,
        11,
        356,
        460,
        407,
  -     39383,
  +     4648,
  +     3970,
  +     660,
        960,
        732,
        460,
        407,
  -     42938,
  +     762,
  +     721,
        4873,
        960,
        732,
        460,
        407,
        6899,
        322,
        960,
  -     5661,
  +     400,
  +     271,
  -     2323,
  ?     ^  -
  +     1132,
  ?     ^^
  +     358,
        13,
        383,
        14802,
        1450,
        11,
  +     17717,
  -     2877,
  ?       --
  +     278,
  ?      +
        290,
  -     2636,
  +     390,
  +     324,
        11,
        508,
  +     2874,
  -     11615,
  -     994,
  -     11,
  +     1130,
  ?       ++
  -     423,
  ?     ^ -
  +     992,
  ?     ^^
  +     994,
  +     11,
  -     42938,
  ?     ---
  +     387,
  ?       +
  +     303,
  +     762,
  +     721,
        4111,
        340,
        11,
        1290,
  -     2029,
  +     450,
  +     659,
  -     674,
  ?       -
  +     267,
  ?     +
  -     3595,
  -     1176,
  +     333,
  +     745,
  +     273,
  +     7182,
  +     263,
        284,
        751,
        393,
  -     31041,
  +     1062,
  +     974,
        13,
        383,
  +     476,
  -     995,
  ?     ^^
  +     335,
  ?     ^^
        481,
  +     6578,
  -     1310,
  ?     ^^
  +     7100,
  ?     ^  +
        3465,
        11,
        4249,
  +     2376,
  -     890,
  ?      ^^
  +     782,
  ?     + ^
  -     3505,
  +     816,
  -     644,
  ?     ^
  +     24419,
  ?     ^  ++
  +     263,
  +     348,
  +     265,
        356,
        910,
        994,
        11,
        475,
        340,
        460,
  -     1239,
  +     497,
  +     332,
        6044,
  -     644,
  ?     ^ ^
  +     348,
  ?     ^ ^
  +     265,
        484,
        750,
        994,
        13,
        632,
        318,
        329,
        514,
        262,
  -     2877,
  -     11,
  +     17717,
  ?      ++ +
  -     2138,
  ?      ^^
  +     278,
  ?      ^
  +     11,
  +     4227,
  +     372,
        11,
        284,
        307,
  +     4648,
  +     3970,
  +     1513,
  +     994,
  +     284,
  -     7256,
  ?     - -
  +     262,
  ?       +
  -     994,
  -     284,
  ?     ^
  +     3684,
  ?     ^^
  -     262,
  -     34419,
  +     16661,
  +     704,
        670,
  -     543,
  ?     ^ ^
  +     348,
  ?     ^ ^
  +     488,
        484,
        508,
  +     46287,
  +     456,
  -     8350,
  ?       --
  +     83,
  +     994,
  +     387,
  +     303,
  -     994,
  ?     ^
  +     294,
  ?     ^
  -     423,
  ?     --
  +     385,
  ?      ++
  -     4145,
        1290,
        523,
        31801,
        306,
  +     1354,
  -     6190,
  ?     ^^
  +     2903,
  ?     ^  +
        13,
        632,
        318,
  -     2138,
  +     4227,
  +     372,
        329,
        514,
        284,
        307,
        994,
  +     4648,
  +     3970,
  +     1513,
  +     284,
  -     7256,
  ?     - -
  +     262,
  ?       +
  -     284,
  +     10536,
  -     262,
  ?       ^
  +     265,
  ?       ^
  -     1049,
  -     4876,
  ?       -
  +     20486,
  ?     ++
  -     5637,
  +     8135,
  +     816,
  +     391,
  -     878,
  ?     ^
  +     278,
  ?     ^
  +     307,
  +     754,
        514,
        960,
        5562,
        422,
  -     777,
  -     21014,
  -     2636,
  -     356,
  -     1011,
  -     3220,
  -     25808,
  -     284,
  -     326,
  ?     -
  +     262,
  ?       +
  +     325,
  +     3032,
  +     1850,
  +     390,
  +     324,
  +     356,
  +     20486,
  +     365,
  +     753,
  +     21468,
  +     36622,
  +     1614,
  +     5092,
  +     261,
  -     2728,
  ?     --
  +     284,
  ?       +
  +     326,
  +     1275,
  +     1904,
        329,
  -     543,
  ?     ^ ^
  +     348,
  ?     ^ ^
  +     488,
        484,
  -     2921,
  +     31986,
  +     303,
        262,
        938,
        1336,
  -     3953,
  +     502,
  +     27345,
  +     260,
        286,
  -     25808,
  +     1614,
  +     5092,
  +     261,
        960,
        5562,
        356,
        994,
  +     1029,
  -     4047,
  -     10568,
  -     326,
  ?      ^
  +     306,
  ?      ^
  -     777,
  +     581,
  +     349,
  +     303,
  -     2636,
  ?       --
  +     326,
  ?     +
  -     2236,
  ?       --
  +     262,
  ?      +
  +     325,
  +     390,
  +     324,
  +     427,
  +     439,
        407,
  -     423,
  ?     --
  +     387,
  ?      ++
  +     303,
        3724,
        287,
  -     23469,
  ?     --
  +     46935,
  ?        ++
  +     259,
        960,
        5562,
  -     428,
  ?      --
  +     294,
  ?     ++
  -     3277,
  ?     -  ^
  +     271,
  ?       ^
  +     34664,
  +     295,
        11,
        739,
        1793,
        11,
  -     2236,
  -     423,
  ?       ^
  +     427,
  ?       ^
  +     439,
  +     387,
  +     303,
        257,
        649,
  +     35122,
  -     4082,
  ?       ^^
  +     400,
  ?       ^
        286,
  -     4925,
  +     13459,
  +     296,
        960,
        392,
        326,
  -     1230,
  +     467,
  +     933,
  +     434,
        286,
        262,
  -     661,
  ?     -
  +     613,
  ?       +
  +     643,
        11,
        416,
        262,
  -     661,
  ?     -
  +     613,
  ?       +
  +     643,
        11,
        329,
        262,
  -     661,
  ?     -
  +     613,
  ?       +
  +     643,
        11,
  -     2236,
  +     427,
  +     439,
        407,
  -     42531,
  ?     --  -
  +     583,
  ?      +
  +     680,
        422,
        262,
  -     4534,
  ?     --
  +     304,
  ?      +
  +     11999,
        13,
        198,
    ]
============================== 1 failed in 0.28s ===============================
