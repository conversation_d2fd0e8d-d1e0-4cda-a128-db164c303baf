"""
Fast tokenization module using cppyy for high-performance text processing.
This module loads C++ code from separate files and provides Python interfaces.
"""

from collections import Counter
from typing import List, Dict
import os

# cppyy integration for fast tokenization
try:
    import cppyy
    CPPYY_AVAILABLE = True
    
    # Get the directory of this file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Load C++ header and implementation
    header_path = os.path.join(current_dir, "fast_tokenizer.hpp")
    cpp_path = os.path.join(current_dir, "fast_tokenizer.cpp")
    
    if os.path.exists(header_path) and os.path.exists(cpp_path):
        # Include the header file
        cppyy.include(header_path)
        
        # Load the implementation
        cppyy.load_library("stdc++")  # For std::regex support
        
        # Compile and load the C++ code
        cppyy.cppdef(f'#include "{header_path}"')
        
        # Load the implementation by reading and compiling the .cpp file
        with open(cpp_path, 'r') as f:
            cpp_code = f.read()
        
        # Remove the #include line since we already included the header
        cpp_lines = cpp_code.split('\n')
        cpp_code_without_include = '\n'.join(line for line in cpp_lines if not line.strip().startswith('#include "fast_tokenizer.hpp"'))
        
        cppyy.cppdef(cpp_code_without_include)
        
        print("C++ fast tokenizer loaded successfully from external files")
    else:
        print(f"Warning: C++ files not found at {header_path} or {cpp_path}")
        CPPYY_AVAILABLE = False
        
except ImportError:
    CPPYY_AVAILABLE = False
    cppyy = None
    print("Warning: cppyy not available, falling back to pure Python implementation")
except Exception as e:
    CPPYY_AVAILABLE = False
    cppyy = None
    print(f"Warning: Failed to load C++ code: {e}, falling back to pure Python implementation")


class FastTokenizerCpp:
    """
    Python wrapper for C++ accelerated tokenizer.
    Provides interfaces that work with Python collections.Counter.
    """
    
    @staticmethod
    def process_text_fast(text: str, pattern: str, special_tokens: List[str]) -> Dict[str, int]:
        """
        Fast text processing using C++ implementation.
        
        Args:
            text: Input text to tokenize
            pattern: Regex pattern for tokenization
            special_tokens: List of special tokens to handle
            
        Returns:
            Dictionary mapping tokens to their frequencies
        """
        if not CPPYY_AVAILABLE:
            raise RuntimeError("cppyy not available")
            
        return cppyy.gbl.FastTokenizer.process_text_fast(text, pattern, special_tokens)
    
    @staticmethod
    def merge_counters(counter1: Dict[str, int], counter2: Dict[str, int]) -> Dict[str, int]:
        """
        Fast counter merging using C++ implementation.
        
        Args:
            counter1: First counter dictionary
            counter2: Second counter dictionary
            
        Returns:
            Merged counter dictionary
        """
        if not CPPYY_AVAILABLE:
            raise RuntimeError("cppyy not available")
            
        return cppyy.gbl.FastTokenizer.merge_counters(counter1, counter2)
    
    @staticmethod
    def update_counter_with_text(counter: Dict[str, int], text: str, pattern: str, special_tokens: List[str]):
        """
        Update existing counter with tokens from text.
        
        Args:
            counter: Existing counter to update (modified in-place)
            text: Input text to tokenize
            pattern: Regex pattern for tokenization
            special_tokens: List of special tokens to handle
        """
        if not CPPYY_AVAILABLE:
            raise RuntimeError("cppyy not available")
            
        cppyy.gbl.FastTokenizer.update_counter_with_text(counter, text, pattern, special_tokens)
    
    @staticmethod
    def convert_from_python_counter(py_counter: Counter) -> Dict[str, int]:
        """
        Convert Python Counter with tuple keys to C++ compatible format.
        
        Args:
            py_counter: Python Counter with tuple[bytes] keys
            
        Returns:
            Dictionary with string keys for C++ compatibility
        """
        if not CPPYY_AVAILABLE:
            raise RuntimeError("cppyy not available")
            
        # Convert Counter items to the format expected by C++
        py_counter_items = []
        for token_tuple, count in py_counter.items():
            if isinstance(token_tuple, tuple):
                # Convert tuple of bytes to list of unsigned char
                token_bytes = list(token_tuple)
            else:
                # Convert string to bytes
                token_bytes = list(str(token_tuple).encode('utf-8'))
            py_counter_items.append((token_bytes, count))
        
        return cppyy.gbl.FastTokenizer.convert_from_python_counter(py_counter_items)
    
    @staticmethod
    def convert_to_python_counter(cpp_counter: Dict[str, int]) -> Counter:
        """
        Convert C++ result back to Python Counter with tuple keys.
        
        Args:
            cpp_counter: C++ result dictionary
            
        Returns:
            Python Counter with tuple[bytes] keys
        """
        if not CPPYY_AVAILABLE:
            raise RuntimeError("cppyy not available")
            
        py_counter_items = cppyy.gbl.FastTokenizer.convert_to_python_counter(cpp_counter)
        
        result = Counter()
        for token_bytes, count in py_counter_items:
            token_tuple = tuple(token_bytes)
            result[token_tuple] = count
        
        return result


def convert_counter_to_cpp_format(counter: Counter) -> Dict[str, int]:
    """
    Convert Python Counter with tuple keys to C++ compatible format.
    
    Args:
        counter: Python Counter with tuple[bytes] keys
        
    Returns:
        Dictionary with string keys for C++ compatibility
    """
    if CPPYY_AVAILABLE:
        return FastTokenizerCpp.convert_from_python_counter(counter)
    else:
        # Fallback implementation
        cpp_counter = {}
        for token_tuple, count in counter.items():
            if isinstance(token_tuple, tuple):
                token_str = b''.join(token_tuple).decode('utf-8', errors='ignore')
            else:
                token_str = str(token_tuple)
            cpp_counter[token_str] = count
        return cpp_counter


def convert_cpp_result_to_counter(cpp_result: Dict[str, int]) -> Counter:
    """
    Convert C++ result back to Python Counter with tuple keys.
    
    Args:
        cpp_result: C++ result dictionary
        
    Returns:
        Python Counter with tuple[bytes] keys
    """
    if CPPYY_AVAILABLE:
        return FastTokenizerCpp.convert_to_python_counter(cpp_result)
    else:
        # Fallback implementation
        result = Counter()
        for token_str, count in cpp_result.items():
            token_bytes = tuple(bytes([b]) for b in token_str.encode('utf-8'))
            result[token_bytes] = count
        return result


def test_fast_tokenizer():
    """Test function for the fast tokenizer."""
    if CPPYY_AVAILABLE:
        print("Testing C++ fast tokenizer...")
        
        # Test text processing
        text = "Hello world! This is a test."
        pattern = r"[a-zA-Z]+|[0-9]+|[^\s\w]+"
        special_tokens = []
        
        result = FastTokenizerCpp.process_text_fast(text, pattern, special_tokens)
        print(f"Tokenization result: {dict(result)}")
        
        # Test counter merging
        counter1 = {"hello": 3, "world": 2}
        counter2 = {"hello": 1, "test": 4}
        
        merged = FastTokenizerCpp.merge_counters(counter1, counter2)
        print(f"Merged counters: {dict(merged)}")
        
        # Test Python Counter conversion
        py_counter = Counter({tuple(b'hello'): 5, tuple(b'world'): 3})
        cpp_format = convert_counter_to_cpp_format(py_counter)
        print(f"Converted to C++ format: {cpp_format}")
        
        back_to_py = convert_cpp_result_to_counter(cpp_format)
        print(f"Converted back to Python: {dict(back_to_py)}")
        
    else:
        print("cppyy not available, skipping C++ tests")


if __name__ == "__main__":
    test_fast_tokenizer()
