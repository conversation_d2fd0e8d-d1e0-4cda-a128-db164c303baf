[project]
name = "cs336_basics"
version = "1.0.3"
description = "CS336 Assignment 1"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "einops>=0.8.1", # tensor operations
    "einx>=0.3.0", # more general tensor operations
    "jaxtyping>=0.3.0", # type hints for pytorch/jax/numpy
    "numpy",
    "psutil>=6.1.1",
    "pytest>=8.3.4",
    "regex>=2024.11.6", # more powerful regex than the builtin `re` module
    "submitit>=1.5.2", # job submission for SLURM
    "tiktoken>=0.9.0",
    "torch~=2.6.0; sys_platform != 'darwin' or platform_machine != 'x86_64'",
    "torch~=2.2.2; sys_platform == 'darwin' and platform_machine == 'x86_64'", # Intel Macs
    "tqdm>=4.67.1",
    "wandb>=0.19.7",
    "scalene>=1.5.51",
]



[tool.setuptools.packages.find]
include = ["cs336_basics"]


[tool.uv]
package = true
python-preference = "managed"

[tool.pytest.ini_options]
log_cli = true
log_cli_level = "WARNING"
addopts = "-s"


[tool.ruff]
line-length = 120

[tool.ruff.lint.extend-per-file-ignores]
# Also ignore `E402` in all `__init__.py` files.
"__init__.py" = ["E402", "F401", "F403", "E501"]

[tool.ruff.lint]
extend-select = ["UP"]
ignore = [
    "F722"
]
