#ifndef FAST_TOKENIZER_HPP
#define FAST_TOKENIZER_HPP

#include <string>
#include <vector>
#include <unordered_map>
#include <regex>
#include <iostream>

/**
 * Fast tokenizer implementation in C++ for high-performance text processing.
 * Designed to work with Python collections.Counter through cppyy.
 */
class FastTokenizer {
public:
    /**
     * Process text and return token frequencies.
     * 
     * @param text Input text to tokenize
     * @param pattern Regex pattern for tokenization
     * @param special_tokens Vector of special tokens to handle separately
     * @return Map of token strings to their frequencies
     */
    static std::unordered_map<std::string, int> process_text_fast(
        const std::string& text, 
        const std::string& pattern,
        const std::vector<std::string>& special_tokens
    );

    /**
     * Merge two token frequency maps.
     * 
     * @param counter1 First token frequency map
     * @param counter2 Second token frequency map
     * @return Merged token frequency map
     */
    static std::unordered_map<std::string, int> merge_counters(
        const std::unordered_map<std::string, int>& counter1,
        const std::unordered_map<std::string, int>& counter2
    );

    /**
     * Update an existing counter with new tokens from text.
     * This method modifies the input counter in-place.
     * 
     * @param counter Existing token frequency map to update
     * @param text Input text to tokenize
     * @param pattern Regex pattern for tokenization
     * @param special_tokens Vector of special tokens to handle separately
     */
    static void update_counter_with_text(
        std::unordered_map<std::string, int>& counter,
        const std::string& text,
        const std::string& pattern,
        const std::vector<std::string>& special_tokens
    );

    /**
     * Convert Python Counter format (with byte tuples) to C++ string format.
     * 
     * @param py_counter_items Vector of pairs representing Python Counter items
     * @return C++ unordered_map with string keys
     */
    static std::unordered_map<std::string, int> convert_from_python_counter(
        const std::vector<std::pair<std::vector<unsigned char>, int>>& py_counter_items
    );

    /**
     * Convert C++ string format back to Python Counter format.
     * 
     * @param cpp_counter C++ unordered_map with string keys
     * @return Vector of pairs suitable for Python Counter construction
     */
    static std::vector<std::pair<std::vector<unsigned char>, int>> convert_to_python_counter(
        const std::unordered_map<std::string, int>& cpp_counter
    );

private:
    /**
     * Internal helper to tokenize a text segment.
     * 
     * @param segment Text segment to tokenize
     * @param token_regex Compiled regex for tokenization
     * @param counter Output counter to update
     */
    static void tokenize_segment(
        const std::string& segment,
        const std::regex& token_regex,
        std::unordered_map<std::string, int>& counter
    );

    /**
     * Create a regex pattern for special tokens.
     * 
     * @param special_tokens Vector of special tokens
     * @return Regex pattern string
     */
    static std::string create_special_tokens_pattern(
        const std::vector<std::string>& special_tokens
    );
};

/**
 * Utility class for performance measurement and debugging.
 */
class TokenizerUtils {
public:
    /**
     * Print counter contents for debugging.
     * 
     * @param counter Token frequency map to print
     * @param max_items Maximum number of items to print
     */
    static void print_counter(
        const std::unordered_map<std::string, int>& counter,
        int max_items = 10
    );

    /**
     * Get total token count from counter.
     * 
     * @param counter Token frequency map
     * @return Total number of tokens
     */
    static int get_total_count(
        const std::unordered_map<std::string, int>& counter
    );

    /**
     * Get most frequent tokens.
     * 
     * @param counter Token frequency map
     * @param top_k Number of top tokens to return
     * @return Vector of pairs (token, frequency) sorted by frequency
     */
    static std::vector<std::pair<std::string, int>> get_most_frequent(
        const std::unordered_map<std::string, int>& counter,
        int top_k = 10
    );
};

#endif // FAST_TOKENIZER_HPP
