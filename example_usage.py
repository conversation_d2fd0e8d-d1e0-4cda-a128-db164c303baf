#!/usr/bin/env python3
"""
Example usage of the fast tokenization functionality with cppyy integration.
This script demonstrates how to use the fast tokenizer with Python collections.Counter.
"""

from collections import Counter
from assign1 import fast_tokenize_with_counter, merge_counters_fast, demo_fast_tokenization

def main():
    """Main example function demonstrating fast tokenization usage."""
    
    print("=" * 60)
    print("Fast Tokenization with cppyy Integration Example")
    print("=" * 60)
    
    # Example 1: Basic tokenization with Counter
    print("\n1. Basic Tokenization Example")
    print("-" * 30)
    
    text = "Hello world! This is a test sentence. <|endoftext|> Another sentence follows."
    special_tokens = ['<|endoftext|>']
    
    # Create an initial Counter with some existing data
    existing_counter = Counter()
    existing_counter[tuple(b'existing_token')] = 10
    
    print(f"Input text: {text}")
    print(f"Special tokens: {special_tokens}")
    print(f"Initial counter: {dict(existing_counter)}")
    
    # Use fast tokenization
    result_counter = fast_tokenize_with_counter(text, special_tokens, existing_counter)
    
    print(f"Total unique tokens: {len(result_counter)}")
    print(f"Total token count: {sum(result_counter.values())}")
    print(f"First 5 tokens: {dict(list(result_counter.items())[:5])}")
    
    # Example 2: Counter merging
    print("\n2. Fast Counter Merging Example")
    print("-" * 30)
    
    # Create two separate counters
    counter1 = Counter()
    counter1[tuple(b'hello')] = 5
    counter1[tuple(b'world')] = 3
    counter1[tuple(b'python')] = 2
    
    counter2 = Counter()
    counter2[tuple(b'hello')] = 2  # This should be merged with counter1
    counter2[tuple(b'cppyy')] = 4
    counter2[tuple(b'fast')] = 1
    
    print(f"Counter 1: {dict(counter1)}")
    print(f"Counter 2: {dict(counter2)}")
    
    # Merge counters using fast implementation
    merged_counter = merge_counters_fast(counter1, counter2)
    
    print(f"Merged counter: {dict(merged_counter)}")
    print(f"Total tokens in merged counter: {sum(merged_counter.values())}")
    
    # Example 3: Processing multiple text chunks
    print("\n3. Multiple Text Chunks Example")
    print("-" * 30)
    
    text_chunks = [
        "First chunk of text with some words.",
        "Second chunk with different content.",
        "Third chunk containing <|endoftext|> special token.",
        "Final chunk to demonstrate accumulation."
    ]
    
    # Accumulate tokens from all chunks
    accumulated_counter = Counter()
    
    for i, chunk in enumerate(text_chunks):
        print(f"Processing chunk {i+1}: {chunk[:30]}...")
        chunk_counter = fast_tokenize_with_counter(chunk, special_tokens)
        accumulated_counter = merge_counters_fast(accumulated_counter, chunk_counter)
    
    print(f"Total unique tokens from all chunks: {len(accumulated_counter)}")
    print(f"Total token count: {sum(accumulated_counter.values())}")
    
    # Show most common tokens
    most_common = accumulated_counter.most_common(5)
    print("Most common tokens:")
    for token_tuple, count in most_common:
        token_str = b''.join(token_tuple).decode('utf-8', errors='ignore')
        print(f"  '{token_str}': {count}")
    
    # Example 4: Performance comparison (if both implementations are available)
    print("\n4. Performance Demonstration")
    print("-" * 30)
    
    # Run the built-in demo
    demo_fast_tokenization()
    
    print("\n" + "=" * 60)
    print("Example completed successfully!")
    print("=" * 60)


def benchmark_example():
    """
    Simple benchmark comparing different approaches.
    This function can be extended to measure performance differences.
    """
    import time
    
    print("\n5. Simple Benchmark")
    print("-" * 30)
    
    # Large text for benchmarking
    large_text = "This is a test sentence. " * 1000
    special_tokens = ['<|endoftext|>']
    
    # Time the tokenization
    start_time = time.time()
    result = fast_tokenize_with_counter(large_text, special_tokens)
    end_time = time.time()
    
    print(f"Processed {len(large_text)} characters")
    print(f"Found {len(result)} unique tokens")
    print(f"Total tokens: {sum(result.values())}")
    print(f"Time taken: {end_time - start_time:.4f} seconds")


if __name__ == "__main__":
    try:
        main()
        benchmark_example()
    except KeyboardInterrupt:
        print("\nExample interrupted by user.")
    except Exception as e:
        print(f"\nError running example: {e}")
        import traceback
        traceback.print_exc()
