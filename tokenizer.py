import regex as re
from abc import ABC
from dataclasses import dataclass
from collections.abc import Iterable, Iterator

PAT = r"""'(?:[sdmt]|ll|ve|re)| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+"""

class Tokenizer(ABC):
    """Abstract interface for a tokenizer."""
    vocab: dict[int, bytes]
    inv_vocab: dict[bytes, int]
    merges: list[tuple[bytes, bytes]]
    special_tokens_pattern: str | None

    def __init__(self, vocab: dict[int, bytes], merges: list[tuple[bytes, bytes]], special_tokens: list[str] | None = None):
        self.vocab = vocab
        self.merges = merges
        self.special_tokens = special_tokens
        self.inv_vocab = {}
        for k, v in vocab.items():
            self.inv_vocab[v] = k
        
        self.special_tokens_pattern = None
        if special_tokens:
            self.special_tokens_pattern = "|".join(map(re.escape, sorted(special_tokens, key=len, reverse=True)))

    @classmethod
    def from_files(cls, vocab_filepath: str, merges_filepath: str, special_tokens: list[str] | None=None):
        raise NotImplementedError

    def encode(self, text: str) -> list[int]:
        tokens = []
        start = 0
        if self.special_tokens_pattern:
            for m0 in re.finditer(self.special_tokens_pattern, text):
                for match in re.finditer(PAT, text[start:m0.start()]):
                    for t in self._tokenize(match.group()):
                        tokens.append(t)
                tokens.append(self.inv_vocab[m0.group().encode("utf-8")])
                start = m0.end()
        
        if start < len(text):
            for match in re.finditer(PAT, text[start:]):
                for t in self._tokenize(match.group()):
                    tokens.append(t)
        return tokens

    def encode_iterable(self, iterable: Iterable[str]) -> Iterator[int]:
        raise NotImplementedError

    def decode(self, ids: list[int]) -> str:
        text_bytes = []
        for id in ids:
            text_bytes.append(self.vocab[id])
        return b''.join(text_bytes).decode('utf-8', errors='replace')

    def _tokenize(self, text: str) -> list[int]:
        word_list = [bytes([b]) for b in text.encode('utf-8')]
        while True:
            #
            merge_candidates = [(i, b''.join(word_list[i:i+2])) for i in range(len(word_list)-1)]
            try:
                best_merge = min( [ (self.inv_vocab[c], i, c) for i, c in merge_candidates if c in self.vocab.values() ])
                idx = best_merge[1]
                word_list = word_list[:idx] + [best_merge[2]] + word_list[idx+2:]
            except ValueError:
                break

        return [self.inv_vocab[b] for b in word_list]
