import regex as re
from abc import ABC
from dataclasses import dataclass
from collections.abc import Iterable, Iterator

PAT = r"""'(?:[sdmt]|ll|ve|re)| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+"""

class Tokenizer(ABC):
    """Abstract interface for a tokenizer."""
    vocab: dict[int, bytes]
    inv_vocab: dict[bytes, int]
    merges: list[tuple[bytes, bytes]]
    special_tokens: list[str] | None = None

    def __init__(self, vocab: dict[int, bytes], merges: list[tuple[bytes, bytes]], special_tokens: list[str] | None = None):
        self.vocab = vocab
        self.merges = merges
        self.special_tokens = special_tokens
        self.inv_vocab = {}
        for k, v in vocab.items():
            self.inv_vocab[v] = k

    @classmethod
    def from_files(cls, vocab_filepath: str, merges_filepath: str, special_tokens: list[str] | None=None):
        raise NotImplementedError

    def encode(self, text: str) -> list[int]:
        tokens = []
        for match in re.finditer(PAT, text):
            if match.group():
                for t in self._tokenize(match.group()):
                    tokens.append(t)
        return tokens

    def encode_iterable(self, iterable: Iterable[str]) -> Iterator[int]:
        raise NotImplementedError

    def decode(self, ids: list[int]) -> str:
        text_bytes = []
        for id in ids:
            text_bytes.append(self.vocab[id])
        return bytes(text_bytes).decode('utf-8')
    
    def _tokenize(self, text: str) -> list[int]:
        text_bytes = text.encode('utf-8')
        n = len(text_bytes)
        i = 0
        token_ids = []
        while i < n:
            j = i
            while j < n and (text_bytes[i:j+1] in self.vocab.values()):
                j += 1
            token_ids.append(self.inv_vocab[text_bytes[i:j]])
            if j == n:
                break
            i = j
        return token_ids